import {
  CHUNK_SIZE,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import {
  createFileAndDownLoad,
  downloadFile,
} from "@/common/export-excel.helper";
import { stringRemoveAccents } from "@/common/helper.ts";
import { getImageVariants } from "@/common/image.helper.ts";
import { useApp } from "@/UseApp";
import {
  DownOutlined,
  FileImageOutlined,
  RightOutlined,
} from "@ant-design/icons";
import { ProjectItemInterface } from "@project/item/interface.ts";
import { ProjectProductInterface } from "@project/product/interface.ts";
import { useFindProjectProductMutation } from "@project/product/service.ts";
import { Form, Table } from "antd";
import dayjs from "dayjs";
import Excel from "exceljs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import getColumnsTableReport from "../../ColumnsTableReport";
import FilterReportZone from "../../FilterReportZone";
import {
  AdvancedFilterFormValueInterface,
  AdvancedFilterInterface,
} from "../../interface";
import ItemProductQuantity from "../../ItemProductQuantity.tsx";
import { useAdvancedFilterFiledsStore } from "../../state.ts";
import { useProjectReportOutletContext } from "../../UseProjectReportOutletContext.tsx";
import CustomerCell from "./CustomerCell.tsx";
import CustomerExchangesCell from "./CustomerExchangesCell.tsx";
import CustomerPhotosCell from "./CustomerPhotosCell.tsx";
import {
  RecordOrderCustomerInterface,
  RecordOrderExchangeInterface,
  RecordOrderPhotoInterface,
  RecordOrderPrizeInterface,
  RecordOrderPurchaseInterface,
  RecordOrderSamplingInterface,
} from "./interface";
import {
  ConditionColumnType,
  DataType,
  fillDataExcel,
  getFixedHeaders,
  OtherStatisticsColumnType,
  ProceedsColumnType,
  processAndLogicalExchange,
  processOrderData,
  processOrLogicalExchange,
  processPurchasesWithoutExchange,
  renderExcelHeader,
  SchemeExchangesDataType,
  updateFeatureSchemeExchangeTotal,
} from "./process.ts";
import {
  useGetFeatureCustomersMutation,
  useGetReportOrdersMutation,
  useReportOrdersQuery,
} from "./service";

export default function ReportCustomerInformationCapturingPage() {
  const {
    componentFeatureQuery,
    projectId,
    componentFeatureId,
    advancedFilterValues,
  } = useProjectReportOutletContext();

  const { setLoading } = useApp();
  const { project } = useProjectReportOutletContext();

  const [filterForm] = Form.useForm();
  const [filter, setFilter] = useState<AdvancedFilterInterface>({
    attendanceStartDate: dayjs().startOf("date").toDate(),
    attendanceEndDate: dayjs().endOf("date").toDate(),
  });
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [isExport, setIsExport] = useState(false);
  const { setFileds: setAdvancedFilterFileds } = useAdvancedFilterFiledsStore();

  const reportOrdersQuery = useReportOrdersQuery(
    projectId,
    componentFeatureId,
    {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
  );

  const getReportOrdersMutation = useGetReportOrdersMutation(
    projectId,
    componentFeatureId,
  );
  const findProjectProductMutation = useFindProjectProductMutation(projectId);
  const getFeatureCustomersMutation = useGetFeatureCustomersMutation(
    projectId,
    componentFeatureId,
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: reportOrdersQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, reportOrdersQuery.data?.count]);

  const setFilterForQuery = useCallback(
    (values: AdvancedFilterFormValueInterface) => {
      setCurrentPage(DEFAULT_CURRENT_PAGE);

      if (_.isEqual(filter, values)) {
        reportOrdersQuery.refetch();
      }
      setFilter(values);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter],
  );

  const onFilterFormFinish = useCallback(() => {
    const values = filterForm.getFieldsValue();

    if (values.attendance) {
      const [attendanceStartDate, attendanceEndDate] = values.attendance;
      values.attendanceStartDate = attendanceStartDate
        ? dayjs(attendanceStartDate).startOf("date").toDate()
        : undefined;
      values.attendanceEndDate = attendanceEndDate
        ? dayjs(attendanceEndDate).endOf("date").toDate()
        : undefined;

      delete values.attendance;
    }
    setFilterForQuery(values);
  }, [filterForm, setFilterForQuery]);

  useEffect(() => {
    setFilterForQuery(advancedFilterValues);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advancedFilterValues]);

  useEffect(() => {
    setAdvancedFilterFileds([
      "Agency phụ trách",
      "Role ghi nhận",
      "Ngày chấm công",
      "Nhân viên ghi nhận",
      "Thông tin khách",
      "Mã/ Tên outlet",
      "Tỉnh/ TP",
      "Quận/ Huyện",
      "Kênh",
      "Nhóm",
      "Loại booth",
      "Trưởng nhóm quản lý",
    ]);
  }, [setAdvancedFilterFileds]);

  const fetchAllData = useCallback(async () => {
    const total = pagination.total!;

    if (total === 0) {
      return [];
    }
    const requests = [];

    for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
      requests.push(
        getReportOrdersMutation.mutateAsync({
          take: CHUNK_SIZE,
          skip: skip,
          ...filter,
        }),
      );
    }

    const results = await Promise.all(requests);
    const allEntities = results.flatMap((result) => result.entities);

    return allEntities;
  }, [filter, getReportOrdersMutation, pagination.total]);

  const exportVertical = useCallback(async () => {
    setIsExport(true);

    const entities = await fetchAllData();

    const featureCustomers = await getFeatureCustomersMutation.mutateAsync();

    const data = entities.flatMap((item) => {
      const {
        recordOrderPurchases,
        recordOrderExchanges,
        recordOrderSamplings,
        recordOrderPrizes,
      } = item;

      const orderData = processOrderData(project, item, featureCustomers);

      const purchasesData = recordOrderPurchases?.map((item) => {
        const { featureOrderProduct, quantity } = item;
        const { projectProduct, price } = featureOrderProduct;

        return [
          "Sản phẩm đã mua",
          "",
          projectProduct.product.code,
          projectProduct.product.name,
          projectProduct.productPackaging?.unit.name,
          quantity,
          price,
          (price ?? 0) * quantity,
        ];
      });

      const exchangesData =
        recordOrderExchanges?.flatMap((item) => {
          const { quantity, featureSchemeExchange } = item;
          const { name: featureSchemeExchangeName } = featureSchemeExchange;
          return item.featureSchemeExchange.exchangeProceeds.map(
            (exchangeProceed) => {
              let name;
              let code;
              let unit;

              const { projectItem, projectProduct } = exchangeProceed;
              if (projectItem) {
                name = projectItem.item.name;
                code = projectItem.item.code;
                unit = projectItem.item.unit?.name;
              }
              if (projectProduct) {
                name = projectProduct.product.name;
                code = projectProduct.product.code;
                unit = projectProduct.productPackaging?.unit.name;
              }

              return [
                "Quà đã nhận",
                featureSchemeExchangeName,
                code,
                name,
                unit,
                exchangeProceed.quantity * quantity,
                "",
                "",
              ];
            },
          );
        }) ?? [];

      let samplingsData = [];
      if (recordOrderSamplings?.length) {
        samplingsData = recordOrderSamplings?.map((item) => {
          return [
            "Sampling đã nhận",
            "",
            item.featureSampling.projectProduct.product.code,
            item.featureSampling.projectProduct.product.name,
            item.featureSampling.unit.name,
            item.quantity,
            "",
            "",
          ];
        });
      } else {
        samplingsData = [["Không nhận sampling", "", "", "", "", "", "", ""]];
      }

      const groupLuckyDraws = _.groupBy(
        recordOrderPrizes,
        (o) => o.projectLuckyDrawResult.projectLuckyDrawItem.projectItem.id,
      );

      const luckyDrawsData = Object.entries(groupLuckyDraws).map(
        ([, luckyDraws]) => {
          const luckyDraw = luckyDraws[0];
          const { projectItem } =
            luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem;
          return [
            "Quà lucky draw",
            luckyDraw.projectLuckyDrawResult.projectLuckyDraw.name,
            projectItem.item.code,
            projectItem.item.name,
            projectItem.item.unit?.name,
            luckyDraws.length,
            "",
            "",
          ];
        },
      );

      const itemsData = _.concat(
        purchasesData,
        exchangesData,
        samplingsData,
        luckyDrawsData,
      );
      const mergedArray = [];
      if (itemsData.length > 0) {
        for (const element of itemsData) {
          mergedArray.push([...orderData, ...(element ?? [])]);
        }
      } else {
        mergedArray.push([...orderData]);
      }

      return mergedArray;
    });

    const headers = [
      ...getFixedHeaders(featureCustomers),
      "Loại data ghi nhận",
      "Tên điều kiện nhận quà",
      "Mã item",
      "Tên item",
      "Quy cách",
      "Số lượng",
      "Đơn giá",
      "Thành tiền",
    ];
    const fileName = "Bao cao don hang";
    await createFileAndDownLoad({
      data,
      headers,
      fileName,
      dateTimeColumns: [6, 7, 19],
    });

    setIsExport(false);
  }, [fetchAllData, getFeatureCustomersMutation, project]);

  const exportHorizontal = useCallback(async () => {
    setIsExport(true);

    const entities = await fetchAllData();
    const totalData = pagination.total ?? 0;

    const { entities: projectProducts } =
      await findProjectProductMutation.mutateAsync({
        take: 0,
      });

    const featureCustomers = await getFeatureCustomersMutation.mutateAsync();
    featureCustomers.sort((a, b) => b.ordinal - a.ordinal);

    // Dùng để làm header
    const featureSchemeExchangesTotal: {
      name: string;
      exchangeConditions: { projectProductId: number }[];
      exchangeProceeds: {
        projectProduct: ProjectProductInterface | null;
        projectItem: ProjectItemInterface | null;
      }[];
    }[] = [];

    const data: DataType[] = [];
    const luckyDrawsHeaders: {
      name: string;
      id: number;
      ordinal: number;
      columnName?: string;
    }[] = [];
    const samplingsHeaders: {
      name: string;
      id: number;
      ordinal: number;
      columnName?: string;
    }[] = [];

    for (const entity of entities) {
      const {
        recordOrderExchanges,
        recordOrderPurchases,
        recordOrderPrizes,
        recordOrderSamplings,
      } = entity;
      const schemeExchangesData: SchemeExchangesDataType[] = [];

      for (const recordOrderExchange of recordOrderExchanges ?? []) {
        const { featureSchemeExchange, quantity: orderQuantity } =
          recordOrderExchange;
        const {
          name: schemeName,
          exchangeProceeds,
          exchangeConditions,
          logical,
        } = featureSchemeExchange;

        updateFeatureSchemeExchangeTotal(
          featureSchemeExchangesTotal,
          exchangeConditions,
          exchangeProceeds,
          schemeName,
        );

        if (logical === "and") {
          processAndLogicalExchange(
            schemeName,
            schemeExchangesData,
            exchangeConditions,
            exchangeProceeds,
            recordOrderPurchases ?? [],
            orderQuantity,
          );
        }

        if (logical === "or") {
          processOrLogicalExchange(
            schemeName,
            schemeExchangesData,
            exchangeConditions,
            exchangeProceeds,
            recordOrderPurchases ?? [],
            orderQuantity,
          );
        }
      }

      schemeExchangesData.sort((a, b) => a.name.localeCompare(b.name));

      processPurchasesWithoutExchange(
        schemeExchangesData,
        recordOrderPurchases ?? [],
        featureSchemeExchangesTotal,
      );

      const orderData = processOrderData(project, entity, featureCustomers);

      const groupLuckyDraws = _.groupBy(
        recordOrderPrizes,
        (o) => o.projectLuckyDrawResult.projectLuckyDrawItem.projectItem.id,
      );

      const prizes = Object.entries(groupLuckyDraws).map(([, luckyDraws]) => {
        const luckyDraw = luckyDraws[0];

        const luckyDrawsHeader = luckyDrawsHeaders.find(
          (item) =>
            item.id ===
            luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem.projectItem
              .id,
        );

        if (!luckyDrawsHeader) {
          luckyDrawsHeaders.push({
            name: luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem
              .projectItem.item.name,
            id: luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem
              .projectItem.id,
            ordinal:
              luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem.ordinal,
          });
        }

        return {
          name: luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem
            .projectItem.item.name,
          quantity: luckyDraws.length,
          id: luckyDraw.projectLuckyDrawResult.projectLuckyDrawItem.projectItem
            .id,
        };
      });

      const groupSamplings = _.groupBy(
        recordOrderSamplings,
        (o) => o.featureSampling.projectProduct.id,
      );

      const samplings = Object.entries(groupSamplings).map(([, samplings]) => {
        const sampling = samplings[0];

        const samplingsHeader = samplingsHeaders.find(
          (item) => item.id === sampling.featureSampling.projectProduct.id,
        );

        if (!samplingsHeader) {
          samplingsHeaders.push({
            name: sampling.featureSampling.projectProduct.product.name,
            id: sampling.featureSampling.projectProduct.id,
            ordinal: sampling.featureSampling.ordinal,
          });
        }

        return {
          name: sampling.featureSampling.projectProduct.product.name,
          quantity: samplings.length,
          id: sampling.featureSampling.projectProduct.id,
        };
      });

      data.push({
        schemeExchangesData,
        orderData,
        prizes,
        samplings,
      });
    }

    const conditionColumns: ConditionColumnType[] = [];
    const proceedsColumns: ProceedsColumnType[] = [];
    const otherStatisticsColumns: OtherStatisticsColumnType[] = [];

    const fixedHeaders = getFixedHeaders(featureCustomers);

    featureSchemeExchangesTotal.sort((a, b) => a.name.localeCompare(b.name));

    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet("data", {});

    renderExcelHeader({
      worksheet,
      fixedHeaders,
      featureSchemeExchangesTotal,
      projectProducts,
      totalData,
      conditionColumns,
      proceedsColumns,
      otherStatisticsColumns,
      luckyDrawsHeaders,
      samplingsHeaders,
    });

    const BEGIN_ROW = 7;

    fillDataExcel(
      worksheet,
      data,
      BEGIN_ROW,
      conditionColumns,
      proceedsColumns,
      otherStatisticsColumns,
      luckyDrawsHeaders,
      samplingsHeaders,
    );

    const fileName = "Bao cao don hang";
    downloadFile(
      workbook,
      `${fileName.trim()} ${dayjs().format("DDMMYY")}.xlsx`,
    );

    setIsExport(false);
  }, [
    fetchAllData,
    findProjectProductMutation,
    getFeatureCustomersMutation,
    pagination.total,
    project,
  ]);

  const exportImages = useCallback(async () => {
    setIsExport(true);

    const entities = await fetchAllData();

    const data = entities.flatMap((entity) => {
      const { recordOrderPhotos } = entity;
      const fixData = processOrderData(
        project,
        entity,
        [],
        [
          "projectId",
          "projectName",
          "projectOutletCode",
          "projectOutletName",
          "projectBoothName",
          "timeIn",
          "timeOut",
          "provinceName",
          "districtName",
          "channelName",
          "subChannelName",
          "agencyName",
          "roleName",
          "employeeUserId",
          "employeeUserName",
          "leaderId",
          "leaderName",
          "entityId",
          "recordOrderCustomers",
          "entityCreatedAt",
        ],
      );
      const mergedData = [];

      for (const recordOrderPhoto of recordOrderPhotos ?? []) {
        if (!recordOrderPhoto.recordPhoto) continue; // Không có recordPhoto ( Hình đã xóa nhưng vẫn còn liên kết)

        const { featurePhoto, image } = recordOrderPhoto.recordPhoto;
        mergedData.push([
          ...fixData,
          featurePhoto.name,
          getImageVariants(image.variants ?? [], "public"),
        ]);
      }
      return mergedData;
    });

    const headers = [
      "ID dự án",
      "Tên dự án",
      "Mã outlet",
      "Tên outlet",
      "Loại booth",
      "Thời gian chấm công vào",
      "Thời gian chấm công ra",
      "Tỉnh/ TP",
      "Quận/ Huyện",
      "Kênh",
      "Nhóm",
      "Agency phụ trách",
      "Role nhân viên chấm công",
      "ID nhân viên chấm công",
      "Họ tên nhân viên chấm công",
      "ID trưởng nhóm quản lý",
      "Họ tên trưởng nhóm quản lý",
      "ID đơn hàng",
      "ID khách",
      "Thời gian ghi nhận",
      "Loại hình",
      "URL hình",
    ];
    const fileName = stringRemoveAccents(
      componentFeatureQuery.data?.name
        ? `Hinh ${componentFeatureQuery.data?.name}`
        : "",
    );

    await createFileAndDownLoad({
      data,
      headers,
      fileName,
      hyperlinkColumns: [22],
      dateTimeColumns: [6, 7, 20],
    });

    setIsExport(false);
  }, [componentFeatureQuery.data?.name, fetchAllData, project]);

  useEffect(() => {
    setLoading(
      reportOrdersQuery.isLoading ||
        reportOrdersQuery.isFetching ||
        reportOrdersQuery.isPending,
    );
  }, [
    reportOrdersQuery.isFetching,
    reportOrdersQuery.isLoading,
    reportOrdersQuery.isPending,
    setLoading,
  ]);

  return (
    <div>
      <h2>{componentFeatureQuery.data?.name}</h2>
      <div className="bg-white p-10 rounded">
        <FilterReportZone
          form={filterForm}
          loading={
            reportOrdersQuery.isFetching ||
            reportOrdersQuery.isLoading ||
            isExport
          }
          fields={["keyword", "roleId", "attendance"]}
          onFinish={onFilterFormFinish}
          customExports={[
            {
              label: "Dữ liệu đơn hàng (dọc)",
              icon: <DownOutlined />,
              onClick: exportVertical,
              key: "exportVertical",
            },
            {
              label: "Dữ liệu đơn hàng (ngang)",
              icon: <RightOutlined />,
              onClick: exportHorizontal,
              key: "exportHorizontal",
            },
            {
              label: "Hình đã chụp",
              icon: <FileImageOutlined />,
              onClick: exportImages,
              key: "exportImage",
            },
          ]}
        />

        <Table
          loading={reportOrdersQuery.isLoading || reportOrdersQuery.isFetching}
          rowKey={"id"}
          pagination={pagination}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          className="mt-3"
          dataSource={reportOrdersQuery.data?.entities.map((item) => ({
            id: item.id,
            projectOutlet:
              item.projectRecordFeature.projectRecord.projectOutlet,
            projectBooth: item.projectRecordFeature.projectRecord.projectBooth,
            projectRecordEmployee:
              item.projectRecordFeature.projectRecordEmployee,
            attendanceIn: item.projectRecordFeature.attendance.in ?? undefined,
            attendanceOut:
              item.projectRecordFeature.attendance.out ?? undefined,
            dataTimestamp: item.dataTimestamp,
            projectAgency:
              item.projectRecordFeature.projectRecord.projectAgency,
            leader: item.projectRecordFeature.projectRecord.leader,
            orderCustomers: item.recordOrderCustomers,
            orderPhotos: item.recordOrderPhotos,
            orderPurchases: item.recordOrderPurchases,
            orderExchanges: item.recordOrderExchanges,
            orderSamplings: item.recordOrderSamplings,
            recordOrderPrizes: item.recordOrderPrizes,
          }))}
          columns={[
            {
              title: "Thông tin khách",
              className: "min-w-[200px]",

              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              render: (_value: any, record: any) => {
                const {
                  orderCustomers,
                }: { orderCustomers: RecordOrderCustomerInterface[] } = record;

                return <CustomerCell orderCustomers={orderCustomers} />;
              },
            },
            ...getColumnsTableReport([
              { tableColumn: "outletCode" },
              { tableColumn: "outletName" },
              // { tableColumn: "boothName" },
              { tableColumn: "attendance" },
              { tableColumn: "dataTimestamp" },
              { tableColumn: "address" },
              // { tableColumn: "channelName" },
              { tableColumn: "subChannelName" },
              // { tableColumn: "agencyName" },
              { tableColumn: "recordEmployee" },
              { tableColumn: "teamLeader" },
            ]),
            {
              title: "Hình đã chụp",
              className: "w-[290px]",
              dataIndex: "orderPhotos",
              render: (orderPhotos: RecordOrderPhotoInterface[]) => (
                <CustomerPhotosCell orderPhotos={orderPhotos} />
              ),
            },
            {
              title: "Sản phẩm đã mua",
              className: "min-w-[150px]",
              dataIndex: "orderPurchases",
              fixed: "right",
              render: (orderPurchases: RecordOrderPurchaseInterface[]) => {
                return orderPurchases.map((orderPurchase, index) => (
                  <ItemProductQuantity
                    isFirst={index === 0}
                    key={orderPurchase.id}
                    quantity={orderPurchase.quantity}
                    unitName={
                      orderPurchase.featureOrderProduct?.projectProduct
                        ?.productPackaging?.unit?.name ?? ""
                    }
                    name={
                      orderPurchase?.featureOrderProduct?.projectProduct
                        ?.product?.name
                    }
                    code={
                      orderPurchase?.featureOrderProduct?.projectProduct
                        ?.product?.code
                    }
                  />
                ));
              },
            },
            {
              title: "Quà đã nhận",
              className: "min-w-[150px]",
              dataIndex: "orderExchanges",
              fixed: "right",
              render: (orderExchanges: RecordOrderExchangeInterface[]) => (
                <CustomerExchangesCell orderExchanges={orderExchanges} />
              ),
            },
            {
              title: "Quà lucky draw",
              dataIndex: "recordOrderPrizes",
              fixed: "right",
              className: "min-w-[150px]",
              hidden: true,
              render: (recordOrderPrizes: RecordOrderPrizeInterface[]) => {
                const group = _.groupBy(
                  recordOrderPrizes,
                  (o) =>
                    o.projectLuckyDrawResult.projectLuckyDrawItem.projectItem
                      .id,
                );

                return Object.entries(group).map(([, group], index) => {
                  return (
                    <ItemProductQuantity
                      isFirst={index === 0}
                      key={group?.[0].id}
                      quantity={group.length}
                      unitName={
                        group?.[0]?.projectLuckyDrawResult?.projectLuckyDrawItem
                          ?.projectItem?.item?.unit?.name ?? ""
                      }
                      name={
                        group?.[0]?.projectLuckyDrawResult?.projectLuckyDrawItem
                          ?.projectItem?.item?.name ?? ""
                      }
                      code={
                        group?.[0]?.projectLuckyDrawResult?.projectLuckyDrawItem
                          ?.projectItem?.item?.code ?? ""
                      }
                    />
                  );
                });
              },
            },
            {
              title: "Sampling đã nhận",
              className: "min-w-[150px]",
              fixed: "right",
              dataIndex: "orderSamplings",
              render: (orderSamplings: RecordOrderSamplingInterface[]) => {
                if (orderSamplings.length === 0) {
                  return "Không nhận sampling";
                }
                return orderSamplings.map((orderSampling, index) => (
                  <ItemProductQuantity
                    isFirst={index === 0}
                    key={orderSampling.id}
                    quantity={orderSampling.quantity}
                    unitName={orderSampling.featureSampling?.unit?.name ?? ""}
                    name={
                      orderSampling.featureSampling?.projectProduct?.product
                        ?.name ?? ""
                    }
                    code={
                      orderSampling.featureSampling?.projectProduct?.product
                        ?.code ?? ""
                    }
                  />
                ));
              },
            },
          ]}
        />
      </div>
    </div>
  );
}
